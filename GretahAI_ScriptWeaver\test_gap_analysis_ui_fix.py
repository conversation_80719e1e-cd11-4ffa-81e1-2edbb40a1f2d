"""
Test script to verify the gap analysis UI fix for interactive selector button interference.

This script tests the new render_interactive_selector_section function and ensures
that interactive selector buttons work correctly without causing UI interference.
"""

import sys
import os
import streamlit as st

# Add the current directory to the path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_interactive_selector_section():
    """Test the new interactive selector section rendering."""
    st.title("🔧 Gap Analysis UI Fix Test")
    st.markdown("Testing the new interactive selector section to prevent UI interference.")
    
    # Test gaps with locator types
    test_gaps = [
        {
            'id': 'username_locator',
            'type': 'locator',
            'description': 'Username input field selector',
            'required': True,
            'suggested_values': ['#username', '.username-input', '[name="username"]']
        },
        {
            'id': 'password_locator',
            'type': 'locator',
            'description': 'Password input field selector',
            'required': True,
            'suggested_values': ['#password', '.password-input', '[name="password"]']
        },
        {
            'id': 'submit_locator',
            'type': 'locator',
            'description': 'Submit button selector',
            'required': True,
            'suggested_values': ['#submit', '.submit-btn', 'button[type="submit"]']
        },
        {
            'id': 'test_data_gap',
            'type': 'test_data',
            'description': 'Test username value',
            'required': True,
            'suggested_values': ['testuser', 'admin', '<EMAIL>']
        }
    ]
    
    # Configuration section
    st.header("Configuration")
    website_url = st.text_input(
        "Website URL for Interactive Selection",
        value="https://example.com",
        placeholder="https://your-target-website.com"
    )
    
    st.markdown("---")
    
    # Test the new interactive selector section
    st.header("Test 1: Interactive Selector Section")
    
    try:
        from core.gap_analysis import render_interactive_selector_section
        
        # Render the interactive selector section
        selector_triggered = render_interactive_selector_section(test_gaps, website_url, avoid_expanders=True)
        
        if selector_triggered:
            st.info("🎯 Interactive selector was triggered - this would normally call st.stop()")
        else:
            st.success("✅ Interactive selector section rendered without triggering")
            
    except Exception as e:
        st.error(f"❌ Error testing interactive selector section: {e}")
        st.exception(e)
    
    st.markdown("---")
    
    # Test the gap filling form
    st.header("Test 2: Gap Filling Form")
    
    try:
        from core.gap_analysis import display_gap_filling_form
        
        # Display the gap filling form
        form_key = "test_gap_form"
        result = display_gap_filling_form(test_gaps, form_key, website_url, avoid_expanders=True)
        
        if result is not None:
            st.success("✅ Gap filling form submitted successfully!")
            st.json(result)
        else:
            st.info("⏳ Gap filling form displayed, waiting for submission")
            
    except Exception as e:
        st.error(f"❌ Error testing gap filling form: {e}")
        st.exception(e)
    
    st.markdown("---")
    
    # Test session state inspection
    st.header("Test 3: Session State Inspection")
    
    # Show interactive locator keys
    interactive_keys = [key for key in st.session_state.keys() if "interactive_locator_" in key]
    
    if interactive_keys:
        st.success(f"✅ Found {len(interactive_keys)} interactive locator keys in session state")
        with st.expander("Interactive Locator Keys", expanded=False):
            for key in interactive_keys:
                value = st.session_state.get(key, "")
                st.write(f"**{key}**: `{value}`")
    else:
        st.info("ℹ️ No interactive locator keys found in session state")
    
    # Show all session state keys for debugging
    with st.expander("All Session State Keys", expanded=False):
        st.write(f"Total keys: {len(st.session_state.keys())}")
        for key in sorted(st.session_state.keys()):
            if not key.startswith('FormSubmitter:'):  # Skip internal Streamlit keys
                value = st.session_state.get(key)
                if isinstance(value, str) and len(value) > 100:
                    value = value[:100] + "..."
                st.write(f"**{key}**: {value}")

if __name__ == "__main__":
    test_interactive_selector_section()
